-- Migration: Create employees table
-- File: 005_create_employees_table.sql

CREATE TABLE IF NOT EXISTS employees (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    billable_time BOOLEAN DEFAULT FALSE,
    hired_date DATE,
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    given_name <PERSON><PERSON><PERSON><PERSON>(255),
    family_name <PERSON><PERSON><PERSON><PERSON>(255),
    display_name VA<PERSON>HA<PERSON>(255),
    print_on_check_name VA<PERSON>HA<PERSON>(255),
    active BOOLEAN DEFAULT TRUE,
    v4id_pseudonym VARCHAR(255),
    primary_phone_number VARCHAR(50),
    metadata_create_time TIMESTAMP,
    metadata_last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_employees_qb_id ON employees(qb_id);
CREATE INDEX IF NOT EXISTS idx_employees_display_name ON employees(display_name);
CREATE INDEX IF NOT EXISTS idx_employees_active ON employees(active);
CREATE INDEX IF NOT EXISTS idx_employees_billable_time ON employees(billable_time);

-- Add comments
COMMENT ON TABLE employees IS 'QuickBooks Employee records';
COMMENT ON COLUMN employees.qb_id IS 'QuickBooks unique identifier';
COMMENT ON COLUMN employees.display_name IS 'Employee display name';
COMMENT ON COLUMN employees.billable_time IS 'Whether employee time is billable';
COMMENT ON COLUMN employees.hired_date IS 'Employee hire date';
