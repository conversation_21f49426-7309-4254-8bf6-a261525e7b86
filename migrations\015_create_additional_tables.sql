-- Migration: Create additional tables
-- File: 015_create_additional_tables.sql

-- Terms table
CREATE TABLE IF NOT EXISTS terms (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    name VA<PERSON>HAR(255),
    active BOOLEAN DEFAULT TRUE,
    type VARCHAR(100),
    due_days INTEGER,
    discount_days INTEGER,
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Transfers table
CREATE TABLE IF NOT EXISTS transfers (
    id SERIAL PRIMARY KEY,
    qb_transfer_id VARCHAR(255) UNIQUE NOT NULL,
    sync_token VARCHAR(255),
    from_account_id VARCHAR(255),
    from_account_name <PERSON><PERSON><PERSON><PERSON>(255),
    to_account_id VARCHAR(255),
    to_account_name VA<PERSON>HA<PERSON>(255),
    amount DECIMAL(15,2),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    txn_date DATE,
    currency_code VARCHAR(10),
    currency_name VARCHAR(50),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    transfer_type VARCHAR(100),
    description TEXT,
    memo TEXT,
    exchange_rate DECIMAL(10,6),
    from_account_ref JSONB,
    to_account_ref JSONB,
    currency_ref JSONB,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Time activities table
CREATE TABLE IF NOT EXISTS time_activities (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    txn_date DATE,
    employee_id VARCHAR(255),
    customer_id VARCHAR(255),
    item_id VARCHAR(255),
    time_charge_id VARCHAR(255),
    billable_status VARCHAR(50),
    taxable BOOLEAN DEFAULT FALSE,
    hourly_rate DECIMAL(10,2),
    cost_rate DECIMAL(10,2),
    hours INTEGER DEFAULT 0,
    minutes INTEGER DEFAULT 0,
    seconds INTEGER DEFAULT 0,
    description TEXT,
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Exchange rates table
CREATE TABLE IF NOT EXISTS exchange_rates (
    id SERIAL PRIMARY KEY,
    source_currency_code VARCHAR(10),
    target_currency_code VARCHAR(10),
    rate DECIMAL(15,8),
    as_of_date DATE,
    metadata_last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(source_currency_code, target_currency_code, as_of_date)
);

-- Deposits table
CREATE TABLE IF NOT EXISTS deposits (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    deposit_to_account_ref_value VARCHAR(255),
    deposit_to_account_ref_name VARCHAR(255),
    cashback_account_ref_value VARCHAR(255),
    cashback_account_ref_name VARCHAR(255),
    cashback_amount DECIMAL(15,2),
    cashback_memo TEXT,
    total_amt DECIMAL(15,2),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    txn_date DATE,
    currency_ref_value VARCHAR(10),
    currency_ref_name VARCHAR(50),
    private_note TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_terms_qb_id ON terms(qb_id);
CREATE INDEX IF NOT EXISTS idx_terms_name ON terms(name);
CREATE INDEX IF NOT EXISTS idx_transfers_qb_transfer_id ON transfers(qb_transfer_id);
CREATE INDEX IF NOT EXISTS idx_transfers_from_account_id ON transfers(from_account_id);
CREATE INDEX IF NOT EXISTS idx_transfers_to_account_id ON transfers(to_account_id);
CREATE INDEX IF NOT EXISTS idx_time_activities_qb_id ON time_activities(qb_id);
CREATE INDEX IF NOT EXISTS idx_time_activities_employee_id ON time_activities(employee_id);
CREATE INDEX IF NOT EXISTS idx_time_activities_customer_id ON time_activities(customer_id);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_source_target ON exchange_rates(source_currency_code, target_currency_code);
CREATE INDEX IF NOT EXISTS idx_deposits_qb_id ON deposits(qb_id);

-- Add comments
COMMENT ON TABLE terms IS 'Payment terms';
COMMENT ON TABLE transfers IS 'Account transfers';
COMMENT ON TABLE time_activities IS 'Time tracking activities';
COMMENT ON TABLE exchange_rates IS 'Currency exchange rates';
COMMENT ON TABLE deposits IS 'Bank deposits';
