-- Migration: Create payment and related tables
-- File: 010_create_payment_tables.sql

-- Payments table
CREATE TABLE IF NOT EXISTS payments (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    customer_ref_value VARCHAR(255),
    customer_ref_name VARCHAR(255),
    deposit_to_account_ref_value VARCHAR(255),
    total_amt DECIMAL(15,2),
    unapplied_amt DECIMAL(15,2),
    process_payment BOOLEAN DEFAULT FALSE,
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    txn_date DATE,
    currency_ref_value VARCHAR(10),
    currency_ref_name VA<PERSON>HAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payment lines table
CREATE TABLE IF NOT EXISTS payment_lines (
    id SERIAL PRIMARY KEY,
    payment_id VARCHAR(255),
    amount DECIMAL(15,2),
    linked_txn_id VARCHAR(255),
    linked_txn_type VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_payments_qb_id ON payments(qb_id);
CREATE INDEX IF NOT EXISTS idx_payments_customer_ref_value ON payments(customer_ref_value);
CREATE INDEX IF NOT EXISTS idx_payments_txn_date ON payments(txn_date);
CREATE INDEX IF NOT EXISTS idx_payment_lines_payment_id ON payment_lines(payment_id);
CREATE INDEX IF NOT EXISTS idx_payment_lines_linked_txn_id ON payment_lines(linked_txn_id);

-- Add comments
COMMENT ON TABLE payments IS 'QuickBooks Payment records';
COMMENT ON TABLE payment_lines IS 'Payment line items linking to invoices';
COMMENT ON COLUMN payments.qb_id IS 'QuickBooks unique identifier';
COMMENT ON COLUMN payments.unapplied_amt IS 'Amount not yet applied to invoices';
