-- Migration: Create accounts table
-- File: 001_create_accounts_table.sql

CREATE TABLE IF NOT EXISTS accounts (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    name VA<PERSON>HAR(255),
    sub_account B<PERSON><PERSON>EAN DEFAULT FALSE,
    fully_qualified_name VA<PERSON>HAR(500),
    active BOOLEAN DEFAULT TRUE,
    classification VARCHAR(100),
    account_type VARCHAR(100),
    account_sub_type VARCHAR(100),
    current_balance DECIMAL(15,2) DEFAULT 0,
    current_balance_with_subaccounts DECIMAL(15,2) DEFAULT 0,
    currency_value VARCHAR(10),
    currency_name VARCHAR(50),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_accounts_qb_id ON accounts(qb_id);
CREATE INDEX IF NOT EXISTS idx_accounts_name ON accounts(name);
CREATE INDEX IF NOT EXISTS idx_accounts_account_type ON accounts(account_type);
CREATE INDEX IF NOT EXISTS idx_accounts_active ON accounts(active);

-- Add comments
COMMENT ON TABLE accounts IS 'QuickBooks Chart of Accounts';
COMMENT ON COLUMN accounts.qb_id IS 'QuickBooks unique identifier';
COMMENT ON COLUMN accounts.fully_qualified_name IS 'Full account name including parent accounts';
COMMENT ON COLUMN accounts.current_balance IS 'Current account balance';
COMMENT ON COLUMN accounts.current_balance_with_subaccounts IS 'Balance including sub-accounts';
