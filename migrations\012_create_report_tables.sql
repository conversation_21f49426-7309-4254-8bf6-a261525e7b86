-- Migration: Create report tables
-- File: 012_create_report_tables.sql

-- Balance sheet table
CREATE TABLE IF NOT EXISTS balance_sheet (
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    total DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(report_date, account_name)
);

-- Cash flow table
CREATE TABLE IF NOT EXISTS cash_flow (
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    section VARCHAR(255),
    account_name VARCHAR(255) NOT NULL,
    total DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(report_date, section, account_name)
);

-- General ledger table
CREATE TABLE IF NOT EXISTS general_ledger (
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    sub_section VARCHAR(255),
    tx_date DATE,
    txn_type VARCHAR(100),
    doc_num VARCHAR(100),
    name VARCHA<PERSON>(255),
    memo TEXT,
    split VARCHAR(255),
    amount DECIMAL(15,2),
    balance DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(report_date, account_name, sub_section, tx_date, txn_type, doc_num)
);

-- Journal report table
CREATE TABLE IF NOT EXISTS journal_report (
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    tx_date DATE,
    txn_type VARCHAR(100),
    doc_num VARCHAR(100),
    name VARCHAR(255),
    memo TEXT,
    account_name VARCHAR(255) NOT NULL,
    debit DECIMAL(15,2) DEFAULT 0,
    credit DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(report_date, tx_date, txn_type, doc_num, account_name)
);

-- Customer balance table
CREATE TABLE IF NOT EXISTS customer_balance (
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    section VARCHAR(255),
    customer_name VARCHAR(255) NOT NULL,
    total DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(report_date, section, customer_name)
);

-- Customer balance detail table
CREATE TABLE IF NOT EXISTS customer_balance_detail (
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    sub_section VARCHAR(255),
    tx_date DATE,
    txn_type VARCHAR(100),
    doc_num VARCHAR(100),
    due_date DATE,
    amount DECIMAL(15,2),
    open_balance DECIMAL(15,2),
    balance DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(report_date, customer_name, sub_section, doc_num)
);

-- Customer income table
CREATE TABLE IF NOT EXISTS customer_income (
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    sub_section VARCHAR(255),
    income DECIMAL(15,2),
    expense DECIMAL(15,2),
    net_income DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(report_date, customer_name, sub_section)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_balance_sheet_report_date ON balance_sheet(report_date);
CREATE INDEX IF NOT EXISTS idx_cash_flow_report_date ON cash_flow(report_date);
CREATE INDEX IF NOT EXISTS idx_general_ledger_report_date ON general_ledger(report_date);
CREATE INDEX IF NOT EXISTS idx_journal_report_report_date ON journal_report(report_date);
CREATE INDEX IF NOT EXISTS idx_customer_balance_report_date ON customer_balance(report_date);
CREATE INDEX IF NOT EXISTS idx_customer_balance_detail_report_date ON customer_balance_detail(report_date);
CREATE INDEX IF NOT EXISTS idx_customer_income_report_date ON customer_income(report_date);

-- Add comments
COMMENT ON TABLE balance_sheet IS 'Balance sheet report data';
COMMENT ON TABLE cash_flow IS 'Cash flow report data';
COMMENT ON TABLE general_ledger IS 'General ledger report data';
COMMENT ON TABLE journal_report IS 'Journal entry report data';
COMMENT ON TABLE customer_balance IS 'Customer balance summary data';
COMMENT ON TABLE customer_balance_detail IS 'Customer balance detail data';
COMMENT ON TABLE customer_income IS 'Customer income statement data';
