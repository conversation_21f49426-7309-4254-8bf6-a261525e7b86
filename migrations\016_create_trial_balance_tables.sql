-- Migration: Create trial balance tables
-- File: 016_create_trial_balance_tables.sql

-- Trial balance reports table
CREATE TABLE IF NOT EXISTS trial_balance_reports (
    id SERIAL PRIMARY KEY,
    report_name VARCHAR(255),
    report_time TIMESTAMP,
    date_macro VARCHAR(100),
    report_basis VARCHAR(100),
    start_period DATE,
    end_period DATE,
    summarize_columns_by VARCHAR(100),
    currency VARCHAR(10),
    no_report_data BOOLEAN DEFAULT FALSE,
    total_debit DECIMAL(15,2),
    total_credit DECIMAL(15,2),
    header_options JSONB,
    columns_info JSONB,
    full_header JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Trial balance accounts table
CREATE TABLE IF NOT EXISTS trial_balance_accounts (
    id SERIAL PRIMARY KEY,
    report_id INTEGER REFERENCES trial_balance_reports(id) ON DELETE CASCADE,
    account_id VARCHAR(255),
    account_name <PERSON><PERSON>HA<PERSON>(255),
    debit_amount DECIMAL(15,2),
    credit_amount DECIMAL(15,2),
    account_type VARCHAR(100),
    account_category VARCHAR(100),
    account_subcategory VARCHAR(100),
    account_group VARCHAR(100),
    row_type VARCHAR(100),
    is_header_account BOOLEAN DEFAULT FALSE,
    is_sub_account BOOLEAN DEFAULT FALSE,
    parent_account VARCHAR(255),
    balance_type VARCHAR(100),
    normal_balance_side VARCHAR(20),
    col_data JSONB,
    summary_data JSONB,
    full_row_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_trial_balance_reports_report_time ON trial_balance_reports(report_time);
CREATE INDEX IF NOT EXISTS idx_trial_balance_reports_start_period ON trial_balance_reports(start_period);
CREATE INDEX IF NOT EXISTS idx_trial_balance_accounts_report_id ON trial_balance_accounts(report_id);
CREATE INDEX IF NOT EXISTS idx_trial_balance_accounts_account_id ON trial_balance_accounts(account_id);
CREATE INDEX IF NOT EXISTS idx_trial_balance_accounts_account_type ON trial_balance_accounts(account_type);

-- Add comments
COMMENT ON TABLE trial_balance_reports IS 'Trial balance report headers';
COMMENT ON TABLE trial_balance_accounts IS 'Trial balance account details';
COMMENT ON COLUMN trial_balance_accounts.normal_balance_side IS 'Debit or Credit normal balance';
