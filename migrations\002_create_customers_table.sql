-- Migration: Create customers table
-- File: 002_create_customers_table.sql

CREATE TABLE IF NOT EXISTS customers (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    given_name <PERSON><PERSON><PERSON><PERSON>(255),
    family_name <PERSON><PERSON><PERSON><PERSON>(255),
    company_name <PERSON><PERSON><PERSON><PERSON>(255),
    display_name <PERSON><PERSON><PERSON><PERSON>(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    taxable BOOLEAN DEFAULT TRUE,
    balance DECIMAL(15,2) DEFAULT 0,
    currency_code VARCHAR(10),
    currency_name VARCHAR(50),
    preferred_delivery_method VARCHAR(50),
    active BOOLEAN DEFAULT TRUE,
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    
    -- Billing Address
    bill_id VARCHAR(255),
    bill_line1 VARCHAR(255),
    bill_city VARCHAR(100),
    bill_state VARCHAR(50),
    bill_postal_code VARCHAR(20),
    bill_lat DECIMAL(10,8),
    bill_long DECIMAL(11,8),
    
    -- Shipping Address
    ship_id VARCHAR(255),
    ship_line1 VARCHAR(255),
    ship_city VARCHAR(100),
    ship_state VARCHAR(50),
    ship_postal_code VARCHAR(20),
    ship_lat DECIMAL(10,8),
    ship_long DECIMAL(11,8),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_customers_qb_id ON customers(qb_id);
CREATE INDEX IF NOT EXISTS idx_customers_display_name ON customers(display_name);
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);
CREATE INDEX IF NOT EXISTS idx_customers_active ON customers(active);

-- Add comments
COMMENT ON TABLE customers IS 'QuickBooks Customer records';
COMMENT ON COLUMN customers.qb_id IS 'QuickBooks unique identifier';
COMMENT ON COLUMN customers.display_name IS 'Customer display name';
COMMENT ON COLUMN customers.balance IS 'Current customer balance';
