-- Migration: Create final tables
-- File: 019_create_final_tables.sql

-- Recurring bills table
CREATE TABLE IF NOT EXISTS recurring_bills (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    sync_token VARCHAR(255),
    balance DECIMAL(15,2),
    total_amt DECIMAL(15,2),
    currency_value VARCHAR(10),
    currency_name VARCHAR(50),
    
    -- Vendor Information
    vendor_id VARCHAR(255),
    vendor_name VARCHAR(255),
    vendor_line1 VARCHAR(255),
    vendor_city VARCHAR(100),
    vendor_state VARCHAR(50),
    vendor_postal VARCHAR(20),
    vendor_lat DECIMAL(10,8),
    vendor_long DECIMAL(11,8),
    
    -- Terms and Account
    sales_term_ref VARCHAR(255),
    ap_account_id VARCHAR(255),
    ap_account_name VARCHAR(255),
    
    -- Recurrence Information
    recur_data_ref VARCHAR(255),
    recur_name <PERSON><PERSON><PERSON><PERSON>(255),
    recur_type VARCHAR(100),
    recur_active BOOLEAN DEFAULT TRUE,
    recur_interval_type VARCHAR(50),
    recur_num_interval INTEGER,
    recur_day_of_month INTEGER,
    recur_next_date DATE,
    
    created_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    last_modified_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Reimburse charges table
CREATE TABLE IF NOT EXISTS reimburse_charges (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    customer_ref_value VARCHAR(255),
    customer_ref_name VARCHAR(255),
    has_been_invoiced BOOLEAN DEFAULT FALSE,
    amount DECIMAL(15,2),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    txn_date DATE,
    currency_ref_value VARCHAR(10),
    currency_ref_name VARCHAR(50),
    private_note TEXT,
    metadata_create_time TIMESTAMP,
    metadata_last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Reimburse charge linked transactions table
CREATE TABLE IF NOT EXISTS reimburse_charge_linked_txns (
    id SERIAL PRIMARY KEY,
    reimburse_charge_id VARCHAR(255),
    txn_id VARCHAR(255),
    txn_type VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Profit and loss details table
CREATE TABLE IF NOT EXISTS profit_and_loss_details (
    id SERIAL PRIMARY KEY,
    report_time TIMESTAMP,
    report_name VARCHAR(255),
    date_macro VARCHAR(100),
    report_basis VARCHAR(100),
    start_period DATE,
    end_period DATE,
    currency VARCHAR(10),
    txn_date DATE,
    txn_type VARCHAR(100),
    txn_id VARCHAR(255),
    doc_num VARCHAR(100),
    name VARCHAR(255),
    name_id VARCHAR(255),
    memo TEXT,
    split_account VARCHAR(255),
    split_account_id VARCHAR(255),
    amount DECIMAL(15,2),
    balance DECIMAL(15,2),
    section_name VARCHAR(255),
    summary_label VARCHAR(255),
    summary_value DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- QB Preferences table
CREATE TABLE IF NOT EXISTS qb_preferences (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    sync_token VARCHAR(255),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- QB Accounting Info Preferences table
CREATE TABLE IF NOT EXISTS qb_accounting_info_prefs (
    id SERIAL PRIMARY KEY,
    preferences_id INTEGER REFERENCES qb_preferences(id) ON DELETE CASCADE,
    use_account_numbers BOOLEAN DEFAULT FALSE,
    track_departments BOOLEAN DEFAULT FALSE,
    class_tracking_per_txn BOOLEAN DEFAULT FALSE,
    class_tracking_per_txn_line BOOLEAN DEFAULT FALSE,
    first_month_of_fiscal_year INTEGER,
    tax_year_month INTEGER,
    tax_form VARCHAR(100),
    customer_terminology VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_recurring_bills_qb_id ON recurring_bills(qb_id);
CREATE INDEX IF NOT EXISTS idx_recurring_bills_vendor_id ON recurring_bills(vendor_id);
CREATE INDEX IF NOT EXISTS idx_reimburse_charges_qb_id ON reimburse_charges(qb_id);
CREATE INDEX IF NOT EXISTS idx_reimburse_charge_linked_txns_reimburse_charge_id ON reimburse_charge_linked_txns(reimburse_charge_id);
CREATE INDEX IF NOT EXISTS idx_profit_and_loss_details_report_time ON profit_and_loss_details(report_time);
CREATE INDEX IF NOT EXISTS idx_qb_preferences_qb_id ON qb_preferences(qb_id);
CREATE INDEX IF NOT EXISTS idx_qb_accounting_info_prefs_preferences_id ON qb_accounting_info_prefs(preferences_id);

-- Add comments
COMMENT ON TABLE recurring_bills IS 'Recurring bill templates';
COMMENT ON TABLE reimburse_charges IS 'Reimbursable charges';
COMMENT ON TABLE profit_and_loss_details IS 'Profit and loss report details';
COMMENT ON TABLE qb_preferences IS 'QuickBooks preferences';
COMMENT ON TABLE qb_accounting_info_prefs IS 'Accounting preferences';
