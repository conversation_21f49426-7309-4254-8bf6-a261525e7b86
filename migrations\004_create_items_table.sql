-- Migration: Create items table
-- File: 004_create_items_table.sql

CREATE TABLE IF NOT EXISTS items (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    description TEXT,
    active BOOLEAN DEFAULT TRUE,
    fully_qualified_name VA<PERSON>HA<PERSON>(500),
    taxable BOOLEAN DEFAULT FALSE,
    unit_price DECIMAL(15,2),
    type VARCHAR(100),
    purchase_desc TEXT,
    purchase_cost DECIMAL(15,2),
    track_qty_on_hand BOOLEAN DEFAULT FALSE,
    qty_on_hand DECIMAL(15,4),
    inv_start_date DATE,
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_items_qb_id ON items(qb_id);
CREATE INDEX IF NOT EXISTS idx_items_name ON items(name);
CREATE INDEX IF NOT EXISTS idx_items_type ON items(type);
CREATE INDEX IF NOT EXISTS idx_items_active ON items(active);
CREATE INDEX IF NOT EXISTS idx_items_taxable ON items(taxable);

-- Add comments
COMMENT ON TABLE items IS 'QuickBooks Items (products and services)';
COMMENT ON COLUMN items.qb_id IS 'QuickBooks unique identifier';
COMMENT ON COLUMN items.name IS 'Item name';
COMMENT ON COLUMN items.unit_price IS 'Default selling price';
COMMENT ON COLUMN items.purchase_cost IS 'Default purchase cost';
COMMENT ON COLUMN items.qty_on_hand IS 'Current quantity on hand';
