-- Migration: Create transaction tables
-- File: 013_create_transaction_tables.sql

-- Transaction list splits table
CREATE TABLE IF NOT EXISTS transaction_list_splits (
    id SERIAL PRIMARY KEY,
    report_id VARCHAR(255),
    account_id VARCHAR(255),
    account_name VARCHAR(255),
    transaction_date DATE,
    transaction_type VARCHAR(100),
    transaction_id VARCHAR(255),
    document_number VARCHAR(100),
    posting_status VARCHAR(50),
    posting_boolean BOOLEAN,
    entity_name VARCHAR(255),
    entity_id VARCHAR(255),
    memo_description TEXT,
    split_account_id VARCHAR(255),
    split_account_name VARCHAR(255),
    amount DECIMAL(15,2),
    raw_amount_value VARCHAR(255),
    row_type VARCHAR(100),
    section_type VARCHAR(100),
    section_position INTEGER,
    is_header BOOLEAN DEFAULT FALSE,
    is_split_line BOOLEAN DEFAULT FALSE,
    is_empty_row BOOLEAN DEFAULT FALSE,
    column_metadata JSONB,
    col_data JSONB,
    full_row_data JSONB,
    full_section_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Transaction customer data table
CREATE TABLE IF NOT EXISTS transaction_customer_data (
    id SERIAL PRIMARY KEY,
    report_id VARCHAR(255),
    customer_id VARCHAR(255),
    customer_name VARCHAR(255),
    transaction_date DATE,
    transaction_type VARCHAR(100),
    transaction_id VARCHAR(255),
    document_number VARCHAR(100),
    posting_status VARCHAR(50),
    posting_boolean BOOLEAN,
    memo_description TEXT,
    account_id VARCHAR(255),
    account_name VARCHAR(255),
    amount DECIMAL(15,2),
    raw_amount_value VARCHAR(255),
    row_type VARCHAR(100),
    section_type VARCHAR(100),
    section_position INTEGER,
    is_header BOOLEAN DEFAULT FALSE,
    is_empty_row BOOLEAN DEFAULT FALSE,
    column_metadata JSONB,
    col_data JSONB,
    full_row_data JSONB,
    full_section_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Transaction listing data table
CREATE TABLE IF NOT EXISTS transaction_listing_data (
    id SERIAL PRIMARY KEY,
    report_id VARCHAR(255),
    transaction_date DATE,
    transaction_type VARCHAR(100),
    transaction_id VARCHAR(255),
    document_number VARCHAR(100),
    posting_status VARCHAR(50),
    posting_boolean BOOLEAN,
    entity_name VARCHAR(255),
    entity_id VARCHAR(255),
    memo_description TEXT,
    account_name VARCHAR(255),
    account_id VARCHAR(255),
    split_account_name VARCHAR(255),
    split_account_id VARCHAR(255),
    amount DECIMAL(15,2),
    raw_amount_value VARCHAR(255),
    row_type VARCHAR(100),
    is_empty_row BOOLEAN DEFAULT FALSE,
    column_metadata JSONB,
    col_data JSONB,
    full_row_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_transaction_list_splits_report_id ON transaction_list_splits(report_id);
CREATE INDEX IF NOT EXISTS idx_transaction_list_splits_account_id ON transaction_list_splits(account_id);
CREATE INDEX IF NOT EXISTS idx_transaction_list_splits_transaction_date ON transaction_list_splits(transaction_date);
CREATE INDEX IF NOT EXISTS idx_transaction_customer_data_report_id ON transaction_customer_data(report_id);
CREATE INDEX IF NOT EXISTS idx_transaction_customer_data_customer_id ON transaction_customer_data(customer_id);
CREATE INDEX IF NOT EXISTS idx_transaction_listing_data_report_id ON transaction_listing_data(report_id);
CREATE INDEX IF NOT EXISTS idx_transaction_listing_data_transaction_date ON transaction_listing_data(transaction_date);

-- Add comments
COMMENT ON TABLE transaction_list_splits IS 'Transaction list with split details';
COMMENT ON TABLE transaction_customer_data IS 'Transaction data by customer';
COMMENT ON TABLE transaction_listing_data IS 'General transaction listing data';
