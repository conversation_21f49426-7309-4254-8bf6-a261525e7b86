-- Migration: Create invoice and related tables
-- File: 007_create_invoice_tables.sql

-- Main invoice table
CREATE TABLE IF NOT EXISTS invoice (
    id SERIAL PRIMARY KEY,
    qb_invoice_id VARCHAR(255) UNIQUE NOT NULL,
    sync_token VARCHAR(255),
    doc_number VARCHAR(100),
    txn_date DATE,
    currency_value VARCHAR(10),
    currency_name VARCHAR(50),
    customer_id VARCHAR(255),
    customer_name VARCHAR(255),
    customer_memo TEXT,
    
    -- Billing Address
    bill_addr_line1 VARCHAR(255),
    bill_addr_line2 VARCHAR(255),
    bill_addr_line3 VARCHAR(255),
    bill_addr_line4 VARCHAR(255),
    bill_addr_lat DECIMAL(10,8),
    bill_addr_long DECIMAL(11,8),
    
    -- Shipping Address
    ship_addr_line1 VARCHAR(255),
    ship_addr_city VARCHAR(100),
    ship_addr_state VARCHAR(50),
    ship_addr_postal_code VARCHAR(20),
    ship_addr_lat DECIMAL(10,8),
    ship_addr_long DECIMAL(11,8),
    
    -- Terms and Amounts
    sales_term_value VARCHAR(255),
    sales_term_name VARCHAR(255),
    due_date DATE,
    total_amt DECIMAL(15,2),
    total_tax DECIMAL(15,2),
    apply_tax_after_discount BOOLEAN DEFAULT FALSE,
    print_status VARCHAR(50),
    email_status VARCHAR(50),
    bill_email VARCHAR(255),
    balance DECIMAL(15,2),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Invoice line items table
CREATE TABLE IF NOT EXISTS invoice_lines (
    id SERIAL PRIMARY KEY,
    invoice_id INTEGER REFERENCES invoice(id) ON DELETE CASCADE,
    qb_invoice_id VARCHAR(255),
    line_num INTEGER,
    amount DECIMAL(15,2),
    detail_type VARCHAR(100),
    sales_item_line_detail JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_invoice_qb_invoice_id ON invoice(qb_invoice_id);
CREATE INDEX IF NOT EXISTS idx_invoice_customer_id ON invoice(customer_id);
CREATE INDEX IF NOT EXISTS idx_invoice_txn_date ON invoice(txn_date);
CREATE INDEX IF NOT EXISTS idx_invoice_due_date ON invoice(due_date);
CREATE INDEX IF NOT EXISTS idx_invoice_lines_invoice_id ON invoice_lines(invoice_id);
CREATE INDEX IF NOT EXISTS idx_invoice_lines_qb_invoice_id ON invoice_lines(qb_invoice_id);

-- Add comments
COMMENT ON TABLE invoice IS 'QuickBooks Invoice records';
COMMENT ON TABLE invoice_lines IS 'Invoice line items';
COMMENT ON COLUMN invoice.qb_invoice_id IS 'QuickBooks unique identifier';
COMMENT ON COLUMN invoice.balance IS 'Outstanding balance on invoice';
