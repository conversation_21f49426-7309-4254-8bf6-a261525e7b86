-- Migration: Create expense and purchase tables
-- File: 011_create_expense_purchase_tables.sql

-- Purchases table (used by expense.js)
CREATE TABLE IF NOT EXISTS purchases (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    account_ref_id VARCHAR(255),
    account_ref_name VARCHAR(255),
    payment_type VARCHAR(100),
    credit BOOLEAN DEFAULT FALSE,
    total_amt DECIMAL(15,2),
    purchase_ex_name VARCHAR(255),
    purchase_ex_value VARCHAR(255),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    txn_date DATE,
    currency_ref_id VARCHAR(10),
    currency_ref_name VARCHAR(50),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Purchase lines table
CREATE TABLE IF NOT EXISTS purchase_lines (
    id SERIAL PRIMARY KEY,
    purchase_id VARCHAR(255),
    line_id VARCHAR(255),
    amount DECIMAL(15,2),
    detail_type VARCHAR(100),
    account_ref_id VARCHAR(255),
    account_ref_name VARCHAR(255),
    billable_status VARCHAR(50),
    tax_code_ref_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Purchase table (used by purchase.js)
CREATE TABLE IF NOT EXISTS purchase (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    sync_token VARCHAR(255),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    txn_date DATE,
    total_amt DECIMAL(15,2),
    payment_type VARCHAR(100),
    entity_id VARCHAR(255),
    entity_name VARCHAR(255),
    entity_type VARCHAR(100),
    account_id VARCHAR(255),
    account_name VARCHAR(255),
    currency_value VARCHAR(10),
    currency_name VARCHAR(50),
    private_note TEXT,
    doc_number VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Purchase order table
CREATE TABLE IF NOT EXISTS purchase_order (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    sync_token VARCHAR(255),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    txn_date DATE,
    doc_number VARCHAR(100),
    total_amt DECIMAL(15,2),
    email_status VARCHAR(50),
    po_status VARCHAR(50),
    vendor_id VARCHAR(255),
    vendor_name VARCHAR(255),
    ap_account_id VARCHAR(255),
    ap_account_name VARCHAR(255),
    currency_value VARCHAR(10),
    currency_name VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_purchases_qb_id ON purchases(qb_id);
CREATE INDEX IF NOT EXISTS idx_purchases_txn_date ON purchases(txn_date);
CREATE INDEX IF NOT EXISTS idx_purchase_lines_purchase_id ON purchase_lines(purchase_id);
CREATE INDEX IF NOT EXISTS idx_purchase_qb_id ON purchase(qb_id);
CREATE INDEX IF NOT EXISTS idx_purchase_order_qb_id ON purchase_order(qb_id);
CREATE INDEX IF NOT EXISTS idx_purchase_order_vendor_id ON purchase_order(vendor_id);

-- Add comments
COMMENT ON TABLE purchases IS 'Purchase transactions (expenses)';
COMMENT ON TABLE purchase_lines IS 'Purchase line items';
COMMENT ON TABLE purchase IS 'Purchase records';
COMMENT ON TABLE purchase_order IS 'Purchase order records';
