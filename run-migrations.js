const fs = require('fs');
const path = require('path');
const pool = require('./db');

// Migration tracking table
const createMigrationTable = `
  CREATE TABLE IF NOT EXISTS migrations (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) UNIQUE NOT NULL,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );
`;

async function runMigrations() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Starting database migrations...');
    
    // Create migrations tracking table
    await client.query(createMigrationTable);
    console.log('✅ Migration tracking table ready');
    
    // Get list of executed migrations
    const { rows: executedMigrations } = await client.query(
      'SELECT filename FROM migrations ORDER BY filename'
    );
    const executedFiles = new Set(executedMigrations.map(row => row.filename));
    
    // Get all migration files
    const migrationsDir = path.join(__dirname, 'migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();
    
    console.log(`📁 Found ${migrationFiles.length} migration files`);
    
    let executedCount = 0;
    
    for (const filename of migrationFiles) {
      if (executedFiles.has(filename)) {
        console.log(`⏭️  Skipping ${filename} (already executed)`);
        continue;
      }
      
      console.log(`🔄 Executing ${filename}...`);
      
      try {
        await client.query('BEGIN');
        
        // Read and execute migration file
        const filePath = path.join(migrationsDir, filename);
        const sql = fs.readFileSync(filePath, 'utf8');
        
        // Execute the migration
        await client.query(sql);
        
        // Record the migration as executed
        await client.query(
          'INSERT INTO migrations (filename) VALUES ($1)',
          [filename]
        );
        
        await client.query('COMMIT');
        console.log(`✅ Successfully executed ${filename}`);
        executedCount++;
        
      } catch (error) {
        await client.query('ROLLBACK');
        console.error(`❌ Error executing ${filename}:`, error.message);
        throw error;
      }
    }
    
    console.log(`\n🎉 Migration completed successfully!`);
    console.log(`📊 Executed ${executedCount} new migrations`);
    console.log(`📊 Total migrations: ${migrationFiles.length}`);
    
    // Show table summary
    const { rows: tables } = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      AND table_name != 'migrations'
      ORDER BY table_name
    `);
    
    console.log(`\n📋 Created ${tables.length} tables:`);
    tables.forEach(table => {
      console.log(`   - ${table.table_name}`);
    });
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  } finally {
    client.release();
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runMigrations()
    .then(() => {
      console.log('\n✨ All done! Database is ready for QuickBooks data.');
      process.exit(0);
    })
    .catch(error => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { runMigrations };
