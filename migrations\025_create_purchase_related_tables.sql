-- Migration: Create purchase-related tables
-- File: 025_create_purchase_related_tables.sql

-- Purchase extension table for NameValue pairs
CREATE TABLE IF NOT EXISTS purchase_ex (
    id SERIAL PRIMARY KEY,
    purchase_id INTEGER,
    name VA<PERSON><PERSON><PERSON>(255),
    value VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Purchase line account detail table
CREATE TABLE IF NOT EXISTS purchase_line_account_detail (
    id SERIAL PRIMARY KEY,
    line_id INTEGER,
    account_id VARCHAR(255),
    account_name VARCHAR(255),
    billable_status VARCHAR(50),
    tax_code_ref VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Purchase order vendor address table
CREATE TABLE IF NOT EXISTS purchase_order_vendor_addr (
    id SERIAL PRIMARY KEY,
    purchase_order_id INTEGER,
    line1 VARCHAR(255),
    line2 VARCHAR(255),
    line3 VARCHAR(255),
    line4 VARCHAR(255),
    lat DECIMAL(10,8),
    long DECIMAL(11,8),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Purchase order shipping address table
CREATE TABLE IF NOT EXISTS purchase_order_ship_addr (
    id SERIAL PRIMARY KEY,
    purchase_order_id INTEGER,
    line1 VARCHAR(255),
    line2 VARCHAR(255),
    line3 VARCHAR(255),
    lat DECIMAL(10,8),
    long DECIMAL(11,8),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Purchase order linked transactions table
CREATE TABLE IF NOT EXISTS purchase_order_linked_txn (
    id SERIAL PRIMARY KEY,
    purchase_order_id INTEGER,
    txn_id VARCHAR(255),
    txn_type VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Purchase order lines table
CREATE TABLE IF NOT EXISTS purchase_order_lines (
    id SERIAL PRIMARY KEY,
    purchase_order_id INTEGER,
    line_id VARCHAR(255),
    line_num INTEGER,
    description TEXT,
    amount DECIMAL(15,2),
    detail_type VARCHAR(100),
    unit_price DECIMAL(15,2),
    qty DECIMAL(15,4),
    billable_status VARCHAR(50),
    item_id VARCHAR(255),
    item_name VARCHAR(255),
    customer_id VARCHAR(255),
    customer_name VARCHAR(255),
    tax_code_ref VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_purchase_ex_purchase_id ON purchase_ex(purchase_id);
CREATE INDEX IF NOT EXISTS idx_purchase_line_account_detail_line_id ON purchase_line_account_detail(line_id);
CREATE INDEX IF NOT EXISTS idx_purchase_order_vendor_addr_purchase_order_id ON purchase_order_vendor_addr(purchase_order_id);
CREATE INDEX IF NOT EXISTS idx_purchase_order_ship_addr_purchase_order_id ON purchase_order_ship_addr(purchase_order_id);
CREATE INDEX IF NOT EXISTS idx_purchase_order_linked_txn_purchase_order_id ON purchase_order_linked_txn(purchase_order_id);
CREATE INDEX IF NOT EXISTS idx_purchase_order_lines_purchase_order_id ON purchase_order_lines(purchase_order_id);

-- Add comments
COMMENT ON TABLE purchase_ex IS 'Purchase extension data (NameValue pairs)';
COMMENT ON TABLE purchase_line_account_detail IS 'Account-based expense line details for purchases';
COMMENT ON TABLE purchase_order_vendor_addr IS 'Vendor addresses for purchase orders';
COMMENT ON TABLE purchase_order_ship_addr IS 'Shipping addresses for purchase orders';
COMMENT ON TABLE purchase_order_linked_txn IS 'Linked transactions for purchase orders';
COMMENT ON TABLE purchase_order_lines IS 'Line items for purchase orders';
