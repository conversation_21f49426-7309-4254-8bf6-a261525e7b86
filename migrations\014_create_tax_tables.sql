-- Migration: Create tax tables
-- File: 014_create_tax_tables.sql

-- Tax agencies table
CREATE TABLE IF NOT EXISTS tax_agencies (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    display_name VA<PERSON>HA<PERSON>(255),
    tax_tracked_on_purchases BOOLEAN DEFAULT FALSE,
    tax_tracked_on_sales BOOLEAN DEFAULT FALSE,
    tax_agency_config JSONB,
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tax classifications table
CREATE TABLE IF NOT EXISTS tax_classifications (
    id SERIAL PRIMARY KEY,
    code VARCHAR(100) UNIQUE NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255),
    description TEXT,
    level INTEGER,
    parent_ref_value VARCHAR(255),
    parent_ref_name <PERSON><PERSON><PERSON><PERSON>(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tax codes table
CREATE TABLE IF NOT EXISTS tax_codes (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    description TEXT,
    active BOOLEAN DEFAULT TRUE,
    hidden BOOLEAN DEFAULT FALSE,
    taxable BOOLEAN DEFAULT FALSE,
    tax_group BOOLEAN DEFAULT FALSE,
    tax_code_config_type VARCHAR(100),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tax rates table
CREATE TABLE IF NOT EXISTS tax_rates (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    description TEXT,
    active BOOLEAN DEFAULT TRUE,
    rate_value DECIMAL(8,4),
    agency_ref_value VARCHAR(255),
    special_tax_type VARCHAR(100),
    display_type VARCHAR(100),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_tax_agencies_qb_id ON tax_agencies(qb_id);
CREATE INDEX IF NOT EXISTS idx_tax_agencies_display_name ON tax_agencies(display_name);
CREATE INDEX IF NOT EXISTS idx_tax_classifications_code ON tax_classifications(code);
CREATE INDEX IF NOT EXISTS idx_tax_classifications_parent_ref_value ON tax_classifications(parent_ref_value);
CREATE INDEX IF NOT EXISTS idx_tax_codes_qb_id ON tax_codes(qb_id);
CREATE INDEX IF NOT EXISTS idx_tax_codes_name ON tax_codes(name);
CREATE INDEX IF NOT EXISTS idx_tax_codes_active ON tax_codes(active);
CREATE INDEX IF NOT EXISTS idx_tax_rates_qb_id ON tax_rates(qb_id);
CREATE INDEX IF NOT EXISTS idx_tax_rates_agency_ref_value ON tax_rates(agency_ref_value);

-- Add comments
COMMENT ON TABLE tax_agencies IS 'Tax agencies for tax reporting';
COMMENT ON TABLE tax_classifications IS 'Tax classification codes';
COMMENT ON TABLE tax_codes IS 'Tax codes for items and transactions';
COMMENT ON TABLE tax_rates IS 'Tax rates and rules';
COMMENT ON COLUMN tax_rates.rate_value IS 'Tax rate as decimal (e.g., 0.0825 for 8.25%)';
