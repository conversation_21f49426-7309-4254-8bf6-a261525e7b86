-- Migration: Create detailed cashflow tables
-- File: 020_create_cashflow_detailed_tables.sql

-- Cashflow report table
CREATE TABLE IF NOT EXISTS cashflow_report (
    report_id SERIAL PRIMARY KEY,
    report_time TIMESTAMP,
    report_name VARCHAR(255),
    date_macro VARCHAR(100),
    start_period DATE,
    end_period DATE,
    summarize_columns_by VARCHAR(100),
    currency VARCHAR(10),
    no_report_data BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Cashflow section table
CREATE TABLE IF NOT EXISTS cashflow_section (
    section_id SERIAL PRIMARY KEY,
    report_id INTEGER REFERENCES cashflow_report(report_id) ON DELETE CASCADE,
    section_name VA<PERSON>HAR(255),
    section_group VARCHAR(255),
    summary_label VARCHAR(255),
    summary_value DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Cashflow line table
CREATE TABLE IF NOT EXISTS cashflow_line (
    line_id SERIAL PRIMARY KEY,
    section_id INTEGER REFERENCES cashflow_section(section_id) ON DELETE CASCADE,
    account_name VARCHAR(255),
    account_id VARCHAR(255),
    amount DECIMAL(15,2),
    line_type VARCHAR(100),
    line_group VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_cashflow_report_report_time ON cashflow_report(report_time);
CREATE INDEX IF NOT EXISTS idx_cashflow_report_start_period ON cashflow_report(start_period);
CREATE INDEX IF NOT EXISTS idx_cashflow_section_report_id ON cashflow_section(report_id);
CREATE INDEX IF NOT EXISTS idx_cashflow_section_section_name ON cashflow_section(section_name);
CREATE INDEX IF NOT EXISTS idx_cashflow_line_section_id ON cashflow_line(section_id);
CREATE INDEX IF NOT EXISTS idx_cashflow_line_account_id ON cashflow_line(account_id);

-- Add comments
COMMENT ON TABLE cashflow_report IS 'Cash flow report headers';
COMMENT ON TABLE cashflow_section IS 'Cash flow report sections';
COMMENT ON TABLE cashflow_line IS 'Cash flow report line items';
COMMENT ON COLUMN cashflow_section.summary_value IS 'Section total amount';
COMMENT ON COLUMN cashflow_line.amount IS 'Line item amount';
