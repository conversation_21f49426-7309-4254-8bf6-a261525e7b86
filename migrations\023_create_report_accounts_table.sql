-- Migration: Create report_accounts table
-- File: 023_create_report_accounts_table.sql

-- Report accounts table
CREATE TABLE IF NOT EXISTS report_accounts (
    id SERIAL PRIMARY KEY,
    report_name VARCHAR(255),
    account_name VARCHAR(255),
    account_type VARCHAR(100),
    detail_acc_type VARCHAR(100),
    account_desc TEXT,
    account_bal DECIMAL(15,2),
    currency VARCHAR(10),
    report_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON>reate indexes
CREATE INDEX IF NOT EXISTS idx_report_accounts_report_name ON report_accounts(report_name);
CREATE INDEX IF NOT EXISTS idx_report_accounts_account_name ON report_accounts(account_name);
CREATE INDEX IF NOT EXISTS idx_report_accounts_account_type ON report_accounts(account_type);
CREATE INDEX IF NOT EXISTS idx_report_accounts_report_time ON report_accounts(report_time);

-- Add comments
COMMENT ON TABLE report_accounts IS 'Account data from various reports';
COMMENT ON COLUMN report_accounts.report_name IS 'Name of the report';
COMMENT ON COLUMN report_accounts.account_name IS 'Account name';
COMMENT ON COLUMN report_accounts.account_type IS 'Account type (Asset, Liability, etc.)';
COMMENT ON COLUMN report_accounts.detail_acc_type IS 'Detailed account type';
COMMENT ON COLUMN report_accounts.account_desc IS 'Account description';
COMMENT ON COLUMN report_accounts.account_bal IS 'Account balance';
COMMENT ON COLUMN report_accounts.currency IS 'Currency code';
COMMENT ON COLUMN report_accounts.report_time IS 'Report generation timestamp';
