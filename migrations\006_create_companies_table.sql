-- Migration: Create companies table
-- File: 006_create_companies_table.sql

CREATE TABLE IF NOT EXISTS companies (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    company_name VA<PERSON>HAR(255),
    legal_name <PERSON><PERSON><PERSON><PERSON>(255),
    
    -- Company Address
    company_addr_id VARCHAR(255),
    company_addr_line1 VARCHAR(255),
    company_addr_city VARCHAR(100),
    company_addr_state VARCHAR(50),
    company_addr_postal_code VARCHAR(20),
    company_addr_lat DECIMAL(10,8),
    company_addr_long DECIMAL(11,8),
    
    -- Customer Communication Address
    cust_comm_addr_id VARCHAR(255),
    cust_comm_addr_line1 VARCHAR(255),
    cust_comm_addr_city VARCHAR(100),
    cust_comm_addr_state VARCHAR(50),
    cust_comm_addr_postal_code VARCHAR(20),
    cust_comm_addr_lat DECIMAL(10,8),
    cust_comm_addr_long DECIMAL(11,8),
    
    -- Legal Address
    legal_addr_id VARCHAR(255),
    legal_addr_line1 VARCHAR(255),
    legal_addr_city VARCHAR(100),
    legal_addr_state VARCHAR(50),
    legal_addr_postal_code VARCHAR(20),
    legal_addr_lat DECIMAL(10,8),
    legal_addr_long DECIMAL(11,8),
    
    -- Contact Information
    primary_phone VARCHAR(50),
    email VARCHAR(255),
    website VARCHAR(255),
    
    -- Company Details
    company_start_date DATE,
    country VARCHAR(100),
    supported_languages VARCHAR(255),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_companies_qb_id ON companies(qb_id);
CREATE INDEX IF NOT EXISTS idx_companies_company_name ON companies(company_name);

-- Add comments
COMMENT ON TABLE companies IS 'QuickBooks Company information';
COMMENT ON COLUMN companies.qb_id IS 'QuickBooks unique identifier';
COMMENT ON COLUMN companies.company_name IS 'Company name';
COMMENT ON COLUMN companies.legal_name IS 'Legal company name';
