{"name": "quickbook-datascript", "version": "1.0.0", "description": "QuickBooks Data Script Service for processing various financial reports and data", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "migrate": "node run-migrations.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"body-parser": "^2.2.0", "cors": "^2.8.5", "express": "^4.18.2", "pg": "^8.16.3"}, "devDependencies": {"nodemon": "^3.0.3"}, "keywords": ["quickbooks", "accounting", "financial-reports", "api", "postgresql"], "author": "", "license": "ISC"}