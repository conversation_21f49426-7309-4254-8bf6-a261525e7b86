-- Migration: Create vendor-related tables
-- File: 017_create_vendor_tables.sql

-- Vendor credits table
CREATE TABLE IF NOT EXISTS vendor_credits (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    sync_token VARCHAR(255),
    vendor_id VARCHAR(255),
    vendor_name VARCHAR(255),
    balance DECIMAL(15,2),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    txn_date DATE,
    currency_code VARCHAR(10),
    currency_name VARCHAR(50),
    total_amount DECIMAL(15,2),
    
    -- Vendor Address
    vendor_addr_id VARCHAR(255),
    vendor_addr_line1 VARCHAR(255),
    vendor_addr_city VARCHAR(100),
    vendor_addr_state VARCHAR(50),
    vendor_addr_postal_code VARCHAR(20),
    vendor_addr_lat DECIMAL(10,8),
    vendor_addr_long DECIMAL(11,8),
    
    -- AP Account
    ap_account_id VARCHAR(255),
    ap_account_name VARCHAR(255),
    
    -- Linked Transaction
    linked_txn_id VARCHAR(255),
    linked_txn_type VARCHAR(100),
    
    -- Query metadata
    query_time TIMESTAMP,
    start_position INTEGER,
    max_results INTEGER,
    total_count INTEGER,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Vendor expense reports table
CREATE TABLE IF NOT EXISTS vendor_expense_reports (
    id SERIAL PRIMARY KEY,
    report_name VARCHAR(255),
    report_time TIMESTAMP,
    date_macro VARCHAR(100),
    report_basis VARCHAR(100),
    start_period DATE,
    end_period DATE,
    summarize_columns_by VARCHAR(100),
    currency VARCHAR(10),
    no_report_data BOOLEAN DEFAULT FALSE,
    total_amount DECIMAL(15,2),
    header_options JSONB,
    columns_info JSONB,
    full_header JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Aged payables table
CREATE TABLE IF NOT EXISTS aged_payables (
    id SERIAL PRIMARY KEY,
    vendor_id VARCHAR(255),
    vendor_name VARCHAR(255),
    amt_current DECIMAL(15,2),
    amt_1_30 DECIMAL(15,2),
    amt_31_60 DECIMAL(15,2),
    amt_61_90 DECIMAL(15,2),
    amt_91_over DECIMAL(15,2),
    total_amt DECIMAL(15,2),
    report_time TIMESTAMP,
    report_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_vendor_credits_qb_id ON vendor_credits(qb_id);
CREATE INDEX IF NOT EXISTS idx_vendor_credits_vendor_id ON vendor_credits(vendor_id);
CREATE INDEX IF NOT EXISTS idx_vendor_credits_txn_date ON vendor_credits(txn_date);
CREATE INDEX IF NOT EXISTS idx_vendor_expense_reports_report_time ON vendor_expense_reports(report_time);
CREATE INDEX IF NOT EXISTS idx_aged_payables_vendor_id ON aged_payables(vendor_id);
CREATE INDEX IF NOT EXISTS idx_aged_payables_report_date ON aged_payables(report_date);

-- Add comments
COMMENT ON TABLE vendor_credits IS 'Vendor credit memos';
COMMENT ON TABLE vendor_expense_reports IS 'Vendor expense report headers';
COMMENT ON TABLE aged_payables IS 'Aged payables report data';
