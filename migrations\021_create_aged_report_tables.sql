-- Migration: Create aged report tables
-- File: 021_create_aged_report_tables.sql

-- Aged payable detail table
CREATE TABLE IF NOT EXISTS aged_payable_detail (
    id SERIAL PRIMARY KEY,
    report_name VARCHAR(255),
    report_time TIMESTAMP,
    date_macro VARCHAR(100),
    start_period DATE,
    end_period DATE,
    currency VARCHAR(10),
    section_name VARCHAR(255),
    row_type VARCHAR(100),
    tx_date DATE,
    txn_type VARCHAR(100),
    doc_num VARCHAR(100),
    vend_name VARCHAR(255),
    vend_id VARCHAR(255),
    due_date DATE,
    past_due INTEGER,
    subt_neg_amount DECIMAL(15,2),
    subt_neg_open_bal DECIMAL(15,2),
    summary_label VARCHAR(255),
    summary_amount DECIMAL(15,2),
    summary_open_bal DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Aged receivable detail table
CREATE TABLE IF NOT EXISTS aged_receivable_detail (
    id SERIAL PRIMARY KEY,
    report_time TIMESTAMP,
    report_name VARCHAR(255),
    start_period DATE,
    end_period DATE,
    currency VARCHAR(10),
    section_name VARCHAR(255),
    txn_date DATE,
    txn_type VARCHAR(100),
    doc_num VARCHAR(100),
    customer_name VARCHAR(255),
    customer_id VARCHAR(255),
    due_date DATE,
    amount DECIMAL(15,2),
    open_balance DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Aged receivables summary table
CREATE TABLE IF NOT EXISTS aged_receivables (
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    current DECIMAL(15,2) DEFAULT 0,
    days_1_30 DECIMAL(15,2) DEFAULT 0,
    days_31_60 DECIMAL(15,2) DEFAULT 0,
    days_61_90 DECIMAL(15,2) DEFAULT 0,
    days_91_over DECIMAL(15,2) DEFAULT 0,
    total DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(report_date, customer_name)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_aged_payable_detail_report_time ON aged_payable_detail(report_time);
CREATE INDEX IF NOT EXISTS idx_aged_payable_detail_vend_id ON aged_payable_detail(vend_id);
CREATE INDEX IF NOT EXISTS idx_aged_payable_detail_section_name ON aged_payable_detail(section_name);
CREATE INDEX IF NOT EXISTS idx_aged_receivable_detail_report_time ON aged_receivable_detail(report_time);
CREATE INDEX IF NOT EXISTS idx_aged_receivable_detail_customer_id ON aged_receivable_detail(customer_id);
CREATE INDEX IF NOT EXISTS idx_aged_receivables_report_date ON aged_receivables(report_date);
CREATE INDEX IF NOT EXISTS idx_aged_receivables_customer_name ON aged_receivables(customer_name);

-- Add comments
COMMENT ON TABLE aged_payable_detail IS 'Detailed aged payables report data';
COMMENT ON TABLE aged_receivable_detail IS 'Detailed aged receivables report data';
COMMENT ON TABLE aged_receivables IS 'Aged receivables summary by customer';
COMMENT ON COLUMN aged_receivables.current IS 'Current balance (not past due)';
COMMENT ON COLUMN aged_receivables.days_1_30 IS 'Balance 1-30 days past due';
COMMENT ON COLUMN aged_receivables.days_31_60 IS 'Balance 31-60 days past due';
COMMENT ON COLUMN aged_receivables.days_61_90 IS 'Balance 61-90 days past due';
COMMENT ON COLUMN aged_receivables.days_91_over IS 'Balance over 90 days past due';
