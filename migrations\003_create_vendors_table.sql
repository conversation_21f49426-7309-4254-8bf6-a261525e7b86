-- Migration: Create vendors table
-- File: 003_create_vendors_table.sql

CREATE TABLE IF NOT EXISTS vendors (
    id SERIAL PRIMARY KEY,
    qb_vendor_id VARCHAR(255) UNIQUE NOT NULL,
    sync_token VARCHAR(255),
    display_name <PERSON><PERSON><PERSON><PERSON>(255),
    company_name <PERSON><PERSON><PERSON><PERSON>(255),
    given_name <PERSON><PERSON><PERSON><PERSON>(255),
    family_name <PERSON><PERSON><PERSON><PERSON>(255),
    print_on_check_name <PERSON><PERSON><PERSON><PERSON>(255),
    active BOOLEAN DEFAULT TRUE,
    vendor_1099 BOOLEAN DEFAULT FALSE,
    balance DECIMAL(15,2) DEFAULT 0,
    acct_num VARCHAR(100),
    bill_rate DECIMAL(10,2),
    currency_ref JSONB,
    bill_addr JSONB,
    term_ref JSON<PERSON>,
    primary_phone VARCHAR(50),
    mobile VARCHAR(50),
    fax VARCHAR(50),
    primary_email_addr VARCHAR(255),
    web_addr VARCHAR(255),
    v4id_pseudonym VARCHAR(255),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_vendors_qb_vendor_id ON vendors(qb_vendor_id);
CREATE INDEX IF NOT EXISTS idx_vendors_display_name ON vendors(display_name);
CREATE INDEX IF NOT EXISTS idx_vendors_active ON vendors(active);
CREATE INDEX IF NOT EXISTS idx_vendors_company_name ON vendors(company_name);

-- Add comments
COMMENT ON TABLE vendors IS 'QuickBooks Vendor records';
COMMENT ON COLUMN vendors.qb_vendor_id IS 'QuickBooks unique identifier';
COMMENT ON COLUMN vendors.display_name IS 'Vendor display name';
COMMENT ON COLUMN vendors.balance IS 'Current vendor balance';
COMMENT ON COLUMN vendors.vendor_1099 IS 'Whether vendor receives 1099 forms';
