-- Migration: Create remaining tables
-- File: 018_create_remaining_tables.sql

-- Credit memos table
CREATE TABLE IF NOT EXISTS credit_memos (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    remaining_credit DECIMAL(15,2),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    doc_number VARCHAR(100),
    txn_date DATE,
    currency_value VARCHAR(10),
    currency_name VARCHAR(50),
    total_tax DECIMAL(15,2),
    customer_value VARCHAR(255),
    customer_name VARCHAR(255),
    customer_memo TEXT,
    bill_addr_id VARCHAR(255),
    ship_addr_id VARCHAR(255),
    free_form_address TEXT,
    total_amt DECIMAL(15,2),
    apply_tax_after_discount BOOLEAN DEFAULT FALSE,
    print_status VARCHAR(50),
    email_status VARCHAR(50),
    bill_email VARCHAR(255),
    balance DECIMAL(15,2),
    metadata_create_time TIMESTAMP,
    metadata_last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Estimates table
CREATE TABLE IF NOT EXISTS estimates (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    doc_number VARCHAR(100),
    txn_date DATE,
    currency_value VARCHAR(10),
    currency_name VARCHAR(50),
    txn_status VARCHAR(50),
    customer_value VARCHAR(255),
    customer_name VARCHAR(255),
    customer_memo TEXT,
    bill_addr_id VARCHAR(255),
    ship_addr_id VARCHAR(255),
    free_form_address TEXT,
    total_amt DECIMAL(15,2),
    apply_tax_after_discount BOOLEAN DEFAULT FALSE,
    print_status VARCHAR(50),
    email_status VARCHAR(50),
    bill_email VARCHAR(255),
    delivery_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Refund receipts table
CREATE TABLE IF NOT EXISTS refund_receipts (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    doc_number VARCHAR(100),
    txn_date DATE,
    currency_value VARCHAR(10),
    currency_name VARCHAR(50),
    total_amt DECIMAL(15,2),
    total_tax DECIMAL(15,2),
    customer_ref_value VARCHAR(255),
    customer_ref_name VARCHAR(255),
    customer_memo TEXT,
    bill_addr_id VARCHAR(255),
    bill_line1 VARCHAR(255),
    bill_line2 VARCHAR(255),
    bill_line3 VARCHAR(255),
    bill_line4 VARCHAR(255),
    bill_lat DECIMAL(10,8),
    bill_long DECIMAL(11,8),
    free_form_address TEXT,
    apply_tax_after_discount BOOLEAN DEFAULT FALSE,
    print_status VARCHAR(50),
    bill_email VARCHAR(255),
    balance DECIMAL(15,2),
    payment_method_ref_value VARCHAR(255),
    payment_method_ref_name VARCHAR(255),
    deposit_to_account_ref_value VARCHAR(255),
    deposit_to_account_ref_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inventory adjustments table
CREATE TABLE IF NOT EXISTS inventory_adjustments (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    sync_token VARCHAR(255),
    adjust_account_value VARCHAR(255),
    adjust_account_name VARCHAR(255),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    doc_number VARCHAR(100),
    txn_date DATE,
    private_note TEXT,
    metadata_create_time TIMESTAMP,
    metadata_last_updated_time TIMESTAMP,
    metadata_last_modified_by_ref VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_credit_memos_qb_id ON credit_memos(qb_id);
CREATE INDEX IF NOT EXISTS idx_credit_memos_customer_value ON credit_memos(customer_value);
CREATE INDEX IF NOT EXISTS idx_estimates_qb_id ON estimates(qb_id);
CREATE INDEX IF NOT EXISTS idx_estimates_customer_value ON estimates(customer_value);
CREATE INDEX IF NOT EXISTS idx_refund_receipts_qb_id ON refund_receipts(qb_id);
CREATE INDEX IF NOT EXISTS idx_inventory_adjustments_qb_id ON inventory_adjustments(qb_id);

-- Add comments
COMMENT ON TABLE credit_memos IS 'Customer credit memos';
COMMENT ON TABLE estimates IS 'Sales estimates/quotes';
COMMENT ON TABLE refund_receipts IS 'Customer refund receipts';
COMMENT ON TABLE inventory_adjustments IS 'Inventory quantity adjustments';
