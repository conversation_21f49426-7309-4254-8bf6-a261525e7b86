-- Migration: Create detailed general ledger table
-- File: 022_create_general_ledger_detailed_table.sql

-- General ledger detailed table (for batch insert function)
CREATE TABLE IF NOT EXISTS general_ledger_detailed (
    id SERIAL PRIMARY KEY,
    report_name VARCHAR(255),
    date_macro VARCHAR(100),
    report_basis VARCHAR(100),
    start_period DATE,
    end_period DATE,
    summarize_columns_by VARCHAR(100),
    currency VARCHAR(10),
    option_name VA<PERSON>HA<PERSON>(255),
    option_value VARCHAR(255),
    col_type VARCHAR(100),
    col_value VARCHAR(255),
    group_type VARCHAR(100),
    account_id VARCHAR(255),
    account_name VARCHAR(255),
    txn_date DATE,
    txn_type VARCHAR(100),
    doc_num VARCHAR(100),
    name VA<PERSON>HA<PERSON>(255),
    memo TEXT,
    split_acc VARCHAR(255),
    amount DECIMAL(15,2),
    balance DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(report_name, account_id, txn_date, txn_type, doc_num)
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_general_ledger_detailed_report_name ON general_ledger_detailed(report_name);
CREATE INDEX IF NOT EXISTS idx_general_ledger_detailed_account_id ON general_ledger_detailed(account_id);
CREATE INDEX IF NOT EXISTS idx_general_ledger_detailed_txn_date ON general_ledger_detailed(txn_date);
CREATE INDEX IF NOT EXISTS idx_general_ledger_detailed_txn_type ON general_ledger_detailed(txn_type);

-- Add comments
COMMENT ON TABLE general_ledger_detailed IS 'Detailed general ledger data with full report context';
COMMENT ON COLUMN general_ledger_detailed.report_name IS 'Name of the report';
COMMENT ON COLUMN general_ledger_detailed.account_id IS 'QuickBooks account ID';
COMMENT ON COLUMN general_ledger_detailed.split_acc IS 'Split account information';
COMMENT ON COLUMN general_ledger_detailed.balance IS 'Running balance';
