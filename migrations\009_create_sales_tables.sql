-- Migration: Create sales and related tables
-- File: 009_create_sales_tables.sql

-- Customer sales table
CREATE TABLE IF NOT EXISTS customer_sales (
    id SERIAL PRIMARY KEY,
    report_name VARCHAR(255),
    report_basis VARCHAR(100),
    start_period DATE,
    end_period DATE,
    summarize_columns_by VARCHAR(100),
    currency VARCHAR(10),
    date_macro VARCHAR(100),
    report_time TIMESTAMP,
    no_report_data BOOLEAN DEFAULT FALSE,
    customer_name VARCHAR(255),
    total_amount DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Class sales table
CREATE TABLE IF NOT EXISTS class_sales (
    id SERIAL PRIMARY KEY,
    report_name VARCHAR(255),
    report_basis VARCHAR(100),
    start_period DATE,
    end_period DATE,
    summarize_columns_by VARCHAR(100),
    currency VARCHAR(10),
    date_macro VARCHAR(100),
    report_time TIMESTAMP,
    no_report_data BOOLEAN DEFAULT FALSE,
    class_name VARCHAR(255),
    total_amount DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Department sales table
CREATE TABLE IF NOT EXISTS department_sales (
    id SERIAL PRIMARY KEY,
    report_name VARCHAR(255),
    report_basis VARCHAR(100),
    start_period DATE,
    end_period DATE,
    summarize_columns_by VARCHAR(100),
    currency VARCHAR(10),
    date_macro VARCHAR(100),
    report_time TIMESTAMP,
    no_report_data BOOLEAN DEFAULT FALSE,
    department_name VARCHAR(255),
    total_amount DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Item sales table
CREATE TABLE IF NOT EXISTS item_sales (
    id SERIAL PRIMARY KEY,
    report_name VARCHAR(255),
    report_basis VARCHAR(100),
    start_period DATE,
    end_period DATE,
    summarize_columns_by VARCHAR(100),
    currency VARCHAR(10),
    date_macro VARCHAR(100),
    report_time TIMESTAMP,
    no_report_data BOOLEAN DEFAULT FALSE,
    item_name VARCHAR(255),
    total_amount DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sales receipt table
CREATE TABLE IF NOT EXISTS sales_receipt (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    sync_token VARCHAR(255),
    doc_number VARCHAR(100),
    txn_date DATE,
    currency VARCHAR(10),
    total_amount DECIMAL(15,2),
    balance DECIMAL(15,2),
    tax_total DECIMAL(15,2),
    print_status VARCHAR(50),
    email_status VARCHAR(50),
    apply_tax_after_discount BOOLEAN DEFAULT FALSE,
    free_form_address TEXT,
    ship_from_address TEXT,
    deposit_to_account VARCHAR(255),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_customer_sales_customer_name ON customer_sales(customer_name);
CREATE INDEX IF NOT EXISTS idx_customer_sales_report_time ON customer_sales(report_time);
CREATE INDEX IF NOT EXISTS idx_class_sales_class_name ON class_sales(class_name);
CREATE INDEX IF NOT EXISTS idx_department_sales_department_name ON department_sales(department_name);
CREATE INDEX IF NOT EXISTS idx_item_sales_item_name ON item_sales(item_name);
CREATE INDEX IF NOT EXISTS idx_sales_receipt_qb_id ON sales_receipt(qb_id);
CREATE INDEX IF NOT EXISTS idx_sales_receipt_txn_date ON sales_receipt(txn_date);

-- Add comments
COMMENT ON TABLE customer_sales IS 'Customer sales report data';
COMMENT ON TABLE class_sales IS 'Class sales report data';
COMMENT ON TABLE department_sales IS 'Department sales report data';
COMMENT ON TABLE item_sales IS 'Item sales report data';
COMMENT ON TABLE sales_receipt IS 'Sales receipt records';
