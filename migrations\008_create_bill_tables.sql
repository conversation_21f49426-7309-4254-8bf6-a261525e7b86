-- Migration: Create bill and related tables
-- File: 008_create_bill_tables.sql

-- Main bills table
CREATE TABLE IF NOT EXISTS bills (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    due_date DATE,
    balance DECIMAL(15,2),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    txn_date DATE,
    currency_code VARCHAR(10),
    currency_name VARCHAR(50),
    total_amount DECIMAL(15,2),
    
    -- Vendor Information
    vendor_id VARCHAR(255),
    vendor_name VARCHAR(255),
    
    -- Vendor Address
    vendor_addr_id VARCHAR(255),
    vendor_addr_line1 VARCHAR(255),
    vendor_addr_city VARCHAR(100),
    vendor_addr_state VARCHAR(50),
    vendor_addr_postal_code VARCHAR(20),
    vendor_addr_lat DECIMAL(10,8),
    vendor_addr_long DECIMAL(11,8),
    
    -- AP Account
    ap_account_id VARCHAR(255),
    ap_account_name VARCHA<PERSON>(255),
    
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bill payments table
CREATE TABLE IF NOT EXISTS bill_payments (
    id SERIAL PRIMARY KEY,
    qb_id VARCHAR(255) UNIQUE NOT NULL,
    vendor_id VARCHAR(255),
    vendor_name VARCHAR(255),
    pay_type VARCHAR(50),
    
    -- Credit Card Account
    cc_account_id VARCHAR(255),
    cc_account_name VARCHAR(255),
    
    -- Bank Account
    bank_account_id VARCHAR(255),
    bank_account_name VARCHAR(255),
    print_status VARCHAR(50),
    total_amount DECIMAL(15,2),
    domain VARCHAR(100),
    sparse BOOLEAN DEFAULT FALSE,
    sync_token VARCHAR(255),
    doc_number VARCHAR(100),
    txn_date DATE,
    currency_code VARCHAR(10),
    currency_name VARCHAR(50),
    create_time TIMESTAMP,
    last_updated_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bill payment lines table
CREATE TABLE IF NOT EXISTS bill_payment_lines (
    id SERIAL PRIMARY KEY,
    bill_payment_id VARCHAR(255),
    amount DECIMAL(15,2),
    linked_txn_id VARCHAR(255),
    linked_txn_type VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_bills_qb_id ON bills(qb_id);
CREATE INDEX IF NOT EXISTS idx_bills_vendor_id ON bills(vendor_id);
CREATE INDEX IF NOT EXISTS idx_bills_due_date ON bills(due_date);
CREATE INDEX IF NOT EXISTS idx_bill_payments_qb_id ON bill_payments(qb_id);
CREATE INDEX IF NOT EXISTS idx_bill_payments_vendor_id ON bill_payments(vendor_id);
CREATE INDEX IF NOT EXISTS idx_bill_payment_lines_bill_payment_id ON bill_payment_lines(bill_payment_id);

-- Add comments
COMMENT ON TABLE bills IS 'QuickBooks Bill records';
COMMENT ON TABLE bill_payments IS 'Bill payment records';
COMMENT ON TABLE bill_payment_lines IS 'Bill payment line items';
